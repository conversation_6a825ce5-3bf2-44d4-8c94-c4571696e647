import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  increment
} from 'firebase/firestore';
import {
  createUserWithEmailAndPassword,
  updateProfile,
  deleteUser as deleteAuthUser,
  sendPasswordResetEmail
} from 'firebase/auth';
import { auth, db } from './firebase';
import type { UserProfile, UserRole } from '../types/firebase';

export interface UserStats {
  totalUsers: number;
  totalStudents: number;
  totalDoctors: number;
  totalAdmins: number;
  activeUsers: number;
  newUsersThisMonth: number;
}

export interface CreateUserData {
  email: string;
  password: string;
  displayName: string;
  role: UserRole;
  department?: string;
  specialty?: string;
  phone?: string;
  status?: 'active' | 'inactive' | 'suspended';
}

// Validation functions
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validatePassword = (password: string): string[] => {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  return errors;
};

const validateDisplayName = (name: string): boolean => {
  return name.trim().length >= 2 && name.trim().length <= 100;
};

const validateUserData = (userData: CreateUserData): string[] => {
  const errors: string[] = [];

  if (!validateEmail(userData.email)) {
    errors.push('Please provide a valid email address');
  }

  if (!validateDisplayName(userData.displayName)) {
    errors.push('Display name must be between 2 and 100 characters');
  }

  const passwordErrors = validatePassword(userData.password);
  errors.push(...passwordErrors);

  if (!['admin', 'doctor', 'student'].includes(userData.role)) {
    errors.push('Invalid user role');
  }

  if (userData.role === 'doctor' && !userData.specialty) {
    errors.push('Specialty is required for doctors');
  }

  if (userData.phone && !/^\+?[\d\s\-\(\)]+$/.test(userData.phone)) {
    errors.push('Please provide a valid phone number');
  }

  return errors;
};

export interface UpdateUserData {
  displayName?: string;
  role?: UserRole;
  department?: string;
  specialty?: string;
  phone?: string;
  status?: 'active' | 'inactive' | 'suspended';
}

/**
 * Get user statistics for admin dashboard
 */
export const getUserStats = async (): Promise<UserStats> => {
  try {
    const usersRef = collection(db, 'users');

    // Get all users
    const allUsersSnapshot = await getDocs(usersRef);
    const allUsers = allUsersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // Calculate statistics
    const totalUsers = allUsers.length;
    const totalStudents = allUsers.filter(user => user.role === 'student').length;
    const totalDoctors = allUsers.filter(user => user.role === 'doctor').length;
    const totalAdmins = allUsers.filter(user => user.role === 'admin').length;
    const activeUsers = allUsers.filter(user => user.status !== 'inactive' && user.status !== 'suspended').length;

    // Calculate new users this month
    const currentDate = new Date();
    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const newUsersThisMonth = allUsers.filter(user => {
      const createdAt = user.createdAt?.toDate ? user.createdAt.toDate() : new Date(user.createdAt);
      return createdAt >= firstDayOfMonth;
    }).length;

    return {
      totalUsers,
      totalStudents,
      totalDoctors,
      totalAdmins,
      activeUsers,
      newUsersThisMonth
    };
  } catch (error) {
    console.error('Error fetching user stats:', error);
    throw error;
  }
};

/**
 * Get all users with optional filtering
 */
export const getUsers = async (
  role?: UserRole,
  limitCount: number = 50,
  lastDoc?: any
): Promise<{ users: UserProfile[], hasMore: boolean, lastDoc: any }> => {
  try {
    // Validate parameters
    if (limitCount < 1 || limitCount > 100) {
      throw new Error('Limit must be between 1 and 100');
    }

    if (role && !['admin', 'doctor', 'student'].includes(role)) {
      throw new Error('Invalid user role filter');
    }

    const usersRef = collection(db, 'users');
    let q = query(usersRef, orderBy('createdAt', 'desc'));

    if (role) {
      q = query(usersRef, where('role', '==', role), orderBy('createdAt', 'desc'));
    }

    if (limitCount) {
      q = query(q, limit(limitCount));
    }

    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    const snapshot = await getDocs(q);
    const users = snapshot.docs
      .map(doc => {
        const data = doc.data();

        // Validate that the document has required fields
        if (!data.email || !data.displayName || !data.role) {
          console.warn(`User document ${doc.id} is missing required fields`);
          return null;
        }

        return {
          uid: doc.id,
          ...data
        } as UserProfile;
      })
      .filter(user => user !== null) as UserProfile[];

    const hasMore = snapshot.docs.length === limitCount;
    const newLastDoc = snapshot.docs[snapshot.docs.length - 1];

    console.log(`📋 Retrieved ${users.length} users${role ? ` with role ${role}` : ''}`);

    return { users, hasMore, lastDoc: newLastDoc };
  } catch (error: any) {
    console.error('Error fetching users:', error);

    if (error.message?.includes('Invalid') || error.message?.includes('must be')) {
      throw error; // Re-throw validation errors as-is
    } else {
      throw new Error('Failed to fetch users. Please try again later');
    }
  }
};

/**
 * Get a single user by ID
 */
export const getUserById = async (userId: string): Promise<UserProfile | null> => {
  try {
    // Validate user ID
    if (!userId || userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    const userDoc = await getDoc(doc(db, 'users', userId));

    if (userDoc.exists()) {
      const userData = userDoc.data();

      // Validate that the document has required fields
      if (!userData.email || !userData.displayName || !userData.role) {
        console.warn(`User document ${userId} is missing required fields`);
        return null;
      }

      return {
        uid: userDoc.id,
        ...userData
      } as UserProfile;
    }

    return null;
  } catch (error: any) {
    console.error('Error fetching user:', error);

    if (error.message?.includes('required')) {
      throw error; // Re-throw validation errors as-is
    } else {
      throw new Error('Failed to fetch user data. Please try again later');
    }
  }
};

/**
 * Create a new user (admin function)
 */
export const createUser = async (userData: CreateUserData): Promise<UserProfile> => {
  try {
    // Validate user data
    const validationErrors = validateUserData(userData);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    // Check if email already exists
    const existingUsers = await searchUsers(userData.email);
    if (existingUsers.length > 0) {
      throw new Error('A user with this email address already exists');
    }

    // Create user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, userData.email, userData.password);
    const user = userCredential.user;

    try {
      // Update display name
      await updateProfile(user, { displayName: userData.displayName });

      // Create user profile in Firestore
      const userProfile: Omit<UserProfile, 'uid'> = {
        email: userData.email,
        displayName: userData.displayName,
        role: userData.role,
        department: userData.department || '',
        specialty: userData.specialty || '',
        phone: userData.phone || '',
        status: userData.status || 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await setDoc(doc(db, 'users', user.uid), userProfile);

      // If creating a student, assign doctors
      if (userData.role === 'student') {
        await assignDoctorsToStudent(user.uid);
      }

      console.log(`✅ User created successfully: ${userData.email}`);

      return {
        uid: user.uid,
        ...userProfile
      };
    } catch (firestoreError) {
      // If Firestore operations fail, clean up the Auth user
      try {
        await deleteAuthUser(user);
      } catch (cleanupError) {
        console.error('Error cleaning up auth user:', cleanupError);
      }
      throw firestoreError;
    }
  } catch (error: any) {
    console.error('Error creating user:', error);

    // Provide more specific error messages
    if (error.code === 'auth/email-already-in-use') {
      throw new Error('This email address is already registered');
    } else if (error.code === 'auth/weak-password') {
      throw new Error('Password is too weak. Please choose a stronger password');
    } else if (error.code === 'auth/invalid-email') {
      throw new Error('Please provide a valid email address');
    } else if (error.message?.includes('Validation failed')) {
      throw error; // Re-throw validation errors as-is
    } else {
      throw new Error('Failed to create user. Please try again later');
    }
  }
};

/**
 * Update user data
 */
export const updateUser = async (userId: string, userData: UpdateUserData): Promise<void> => {
  try {
    // Validate user ID
    if (!userId || userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    // Validate update data
    if (userData.displayName && !validateDisplayName(userData.displayName)) {
      throw new Error('Display name must be between 2 and 100 characters');
    }

    if (userData.role && !['admin', 'doctor', 'student'].includes(userData.role)) {
      throw new Error('Invalid user role');
    }

    if (userData.role === 'doctor' && !userData.specialty) {
      throw new Error('Specialty is required for doctors');
    }

    if (userData.phone && !/^\+?[\d\s\-\(\)]+$/.test(userData.phone)) {
      throw new Error('Please provide a valid phone number');
    }

    // Check if user exists
    const userExists = await getUserById(userId);
    if (!userExists) {
      throw new Error('User not found');
    }

    const updateData = {
      ...userData,
      updatedAt: new Date()
    };

    await updateDoc(doc(db, 'users', userId), updateData);
    console.log(`✅ User updated successfully: ${userId}`);
  } catch (error: any) {
    console.error('Error updating user:', error);

    if (error.message?.includes('not found') || error.message?.includes('required') ||
        error.message?.includes('Invalid') || error.message?.includes('must be')) {
      throw error; // Re-throw validation/not found errors as-is
    } else {
      throw new Error('Failed to update user. Please try again later');
    }
  }
};

/**
 * Delete a user
 */
export const deleteUser = async (userId: string): Promise<void> => {
  try {
    // Delete user document from Firestore
    await deleteDoc(doc(db, 'users', userId));

    // Note: Deleting from Firebase Auth requires admin SDK on backend
    // For now, we'll just mark as deleted in Firestore
    console.log('User deleted from Firestore. Auth deletion requires backend implementation.');
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};

/**
 * Assign doctors to a student
 */
const assignDoctorsToStudent = async (studentId: string): Promise<void> => {
  try {
    // Get available doctors
    const { users: doctors } = await getUsers('doctor', 10);

    // Assign ALL available real doctors (no artificial limit, no mock data)
    const assignedDoctors = doctors;

    await setDoc(doc(db, 'doctorAssignments', studentId), {
      doctors: assignedDoctors.map(doctor => ({
        id: doctor.uid,
        name: doctor.displayName,
        specialty: doctor.specialty || 'General Practice',
        department: doctor.department || 'Primary Care',
        assignedDate: new Date().toISOString()
      })),
      updatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error assigning doctors:', error);
    throw error;
  }
};

/**
 * Send password reset email
 */
export const sendUserPasswordReset = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Error sending password reset:', error);
    throw error;
  }
};

/**
 * Search users by name or email
 */
export const searchUsers = async (searchTerm: string, role?: UserRole): Promise<UserProfile[]> => {
  try {
    const usersRef = collection(db, 'users');
    let q = query(usersRef);

    if (role) {
      q = query(usersRef, where('role', '==', role));
    }

    const snapshot = await getDocs(q);
    const users = snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    })) as UserProfile[];

    // Filter by search term (client-side filtering for simplicity)
    const filteredUsers = users.filter(user =>
      user.displayName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return filteredUsers;
  } catch (error) {
    console.error('Error searching users:', error);
    throw error;
  }
};
