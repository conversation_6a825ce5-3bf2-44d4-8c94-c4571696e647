// Request Management Service
// Handles student requests to connect with doctors

import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from './firebase';
import { getAllUsers } from './authService';

export interface ConnectionRequest {
  id: string;
  studentId: string;
  studentName: string;
  studentEmail: string;
  doctorId: string;
  doctorName: string;
  doctorEmail: string;
  message?: string;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: Timestamp;
  updatedAt: Timestamp;
  respondedAt?: Timestamp;
}

/**
 * Create a connection request from student to doctor
 */
export const createConnectionRequest = async (
  studentId: string,
  doctorId: string,
  message?: string
): Promise<boolean> => {
  try {
    // Get student and doctor details
    const allUsers = await getAllUsers();
    const student = allUsers.find(u => u.uid === studentId);
    const doctor = allUsers.find(u => u.uid === doctorId);

    if (!student || !doctor) {
      throw new Error('Student or doctor not found');
    }

    // Check if request already exists
    const existingRequestQuery = query(
      collection(db, 'connectionRequests'),
      where('studentId', '==', studentId),
      where('doctorId', '==', doctorId),
      where('status', '==', 'pending')
    );

    const existingRequests = await getDocs(existingRequestQuery);
    if (!existingRequests.empty) {
      console.log('Request already exists');
      return false;
    }

    // Create new request
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const requestData: Omit<ConnectionRequest, 'id'> = {
      studentId,
      studentName: student.displayName || student.email || 'Unknown Student',
      studentEmail: student.email,
      doctorId,
      doctorName: doctor.displayName || doctor.email || 'Unknown Doctor',
      doctorEmail: doctor.email,
      message: message || '',
      status: 'pending',
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp
    };

    await setDoc(doc(db, 'connectionRequests', requestId), requestData);

    // Send notification to doctor
    const { notifyDoctorOfConnectionRequest } = await import('./notificationService');
    await notifyDoctorOfConnectionRequest(doctorId, requestData.studentName, requestId);

    console.log(`✅ Connection request created: ${requestId}`);
    return true;
  } catch (error) {
    console.error('Error creating connection request:', error);
    return false;
  }
};

/**
 * Get all pending requests for a doctor
 */
export const getDoctorRequests = async (doctorId: string): Promise<ConnectionRequest[]> => {
  try {
    const requestsQuery = query(
      collection(db, 'connectionRequests'),
      where('doctorId', '==', doctorId),
      where('status', '==', 'pending'),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(requestsQuery);
    const requests = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ConnectionRequest[];

    console.log(`📋 Found ${requests.length} pending requests for doctor ${doctorId}`);
    return requests;
  } catch (error) {
    console.error('Error getting doctor requests:', error);
    return [];
  }
};

/**
 * Get all requests made by a student
 */
export const getStudentRequests = async (studentId: string): Promise<ConnectionRequest[]> => {
  try {
    const requestsQuery = query(
      collection(db, 'connectionRequests'),
      where('studentId', '==', studentId),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(requestsQuery);
    const requests = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ConnectionRequest[];

    return requests;
  } catch (error) {
    console.error('Error getting student requests:', error);
    return [];
  }
};

/**
 * Accept a connection request
 */
export const acceptConnectionRequest = async (requestId: string): Promise<boolean> => {
  try {
    // Get request details first
    const requestDoc = await getDoc(doc(db, 'connectionRequests', requestId));
    if (!requestDoc.exists()) {
      throw new Error('Request not found');
    }

    const requestData = requestDoc.data() as ConnectionRequest;

    await updateDoc(doc(db, 'connectionRequests', requestId), {
      status: 'accepted',
      respondedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // Send notification to student
    const { notifyStudentOfAcceptedRequest } = await import('./notificationService');
    await notifyStudentOfAcceptedRequest(
      requestData.studentId,
      requestData.doctorName,
      requestId
    );

    console.log(`✅ Request ${requestId} accepted`);
    return true;
  } catch (error) {
    console.error('Error accepting request:', error);
    return false;
  }
};

/**
 * Reject a connection request
 */
export const rejectConnectionRequest = async (requestId: string): Promise<boolean> => {
  try {
    await updateDoc(doc(db, 'connectionRequests', requestId), {
      status: 'rejected',
      respondedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    console.log(`❌ Request ${requestId} rejected`);
    return true;
  } catch (error) {
    console.error('Error rejecting request:', error);
    return false;
  }
};

/**
 * Get request statistics for a doctor
 */
export const getDoctorRequestStats = async (doctorId: string) => {
  try {
    const allRequestsQuery = query(
      collection(db, 'connectionRequests'),
      where('doctorId', '==', doctorId)
    );

    const snapshot = await getDocs(allRequestsQuery);
    const requests = snapshot.docs.map(doc => doc.data());

    const pending = requests.filter(r => r.status === 'pending').length;
    const accepted = requests.filter(r => r.status === 'accepted').length;
    const rejected = requests.filter(r => r.status === 'rejected').length;

    return {
      total: requests.length,
      pending,
      accepted,
      rejected
    };
  } catch (error) {
    console.error('Error getting request stats:', error);
    return { total: 0, pending: 0, accepted: 0, rejected: 0 };
  }
};
