// Doctor Assignment Service
// Manages the assignment and retrieval of doctors for students

import { collection, getDocs, doc, setDoc, getDoc, deleteDoc } from 'firebase/firestore';
import { db } from './firebase';
import { getAllUsers } from './authService';

export interface Doctor {
  id: string;
  name: string;
  specialty: string;
  department: string;
  avatar?: string;
  online?: boolean;
  assigned?: boolean;
  requested?: boolean;
  email?: string;
  uid?: string;
}

export interface AssignedDoctor extends Doctor {
  assignedDate: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
}

// Core specialties that every student should have access to
const CORE_SPECIALTIES = ['General Practice', 'Mental Health', 'Emergency Medicine'];

/**
 * Get all doctors from Firebase
 */
export const getDoctorsFromFirebase = async (): Promise<Doctor[]> => {
  try {
    console.log('🔍 Starting getDoctorsFromFirebase...');
    const allUsers = await getAllUsers();
    console.log(`📊 Total users found: ${allUsers.length}`);

    // Debug: Log all users to see what we have
    console.log('All users sample:', allUsers.slice(0, 3).map(u => ({
      uid: u.uid,
      email: u.email,
      role: u.role,
      displayName: u.displayName,
      hasUid: !!u.uid
    })));

    const doctorUsers = allUsers.filter(user => user.role === 'doctor');
    console.log(`👨‍⚕️ Users with doctor role: ${doctorUsers.length}`);

    // Debug: Log doctor users specifically
    if (doctorUsers.length > 0) {
      console.log('Doctor users found:', doctorUsers.map(u => ({
        uid: u.uid,
        email: u.email,
        displayName: u.displayName,
        specialty: u.specialty,
        department: u.department
      })));
    }

    // Log doctors without UIDs for debugging
    const doctorsWithoutUIDs = doctorUsers.filter(user => !user.uid);
    if (doctorsWithoutUIDs.length > 0) {
      console.warn(`⚠️ Found ${doctorsWithoutUIDs.length} doctors without UIDs:`, doctorsWithoutUIDs);
    }

    const doctors = doctorUsers
      .filter(user => user.uid) // Ensure user has a valid UID
      .map(user => ({
        id: user.uid, // Primary ID
        uid: user.uid, // Backup ID
        name: user.displayName || user.email?.split('@')[0] || 'Unknown Doctor',
        specialty: user.specialty || 'General Practice',
        department: user.department || 'Primary Care',
        email: user.email,
        avatar: '',
        online: false, // You can update this with real presence logic if needed
        assigned: false,
        requested: false
      }));

    console.log(`✅ Final processed doctors: ${doctors.length}`);

    // Debug: Log final doctor objects
    if (doctors.length > 0) {
      console.log('Final doctor objects:', doctors.map(d => ({
        id: d.id,
        name: d.name,
        specialty: d.specialty,
        email: d.email
      })));
    }

    // Debug: Log doctors without proper names
    const doctorsWithoutNames = doctors.filter(d => d.name === 'Unknown Doctor');
    if (doctorsWithoutNames.length > 0) {
      console.warn(`Found ${doctorsWithoutNames.length} doctors without proper names:`, doctorsWithoutNames);
    }

    return doctors;
  } catch (error) {
    console.error('❌ Error fetching doctors from Firebase:', error);
    console.error('Error details:', error);
    return [];
  }
};

/**
 * Smart doctor assignment algorithm
 * Ensures every student gets essential care coverage
 * ONLY USES REAL DOCTORS FROM DATABASE - NO MOCK DATA
 */
export const assignDoctorsToStudent = async (studentId: string): Promise<AssignedDoctor[]> => {
  try {
    const availableDoctors = await getDoctorsFromFirebase();
    console.log(`🏥 Found ${availableDoctors.length} real doctors available for assignment`);

    if (availableDoctors.length === 0) {
      console.warn('⚠️ No doctors available in database for assignment');
      return [];
    }

    // Assign all available doctors (no artificial limit of 5)
    // This ensures students can see and chat with ALL doctors in the system
    const assignedDoctors = formatAssignedDoctors(availableDoctors);
    await saveAssignedDoctors(studentId, assignedDoctors);

    console.log(`✅ Assigned ${assignedDoctors.length} real doctors to student ${studentId}`);
    return assignedDoctors;

  } catch (error) {
    console.error('Error assigning doctors:', error);
    return [];
  }
};

/**
 * Format doctors as AssignedDoctor objects
 */
const formatAssignedDoctors = (doctors: Doctor[]): AssignedDoctor[] => {
  return doctors.map((doctor) => ({
    ...doctor,
    assignedDate: new Date().toISOString(),
    assigned: true
    // No mock lastMessage, lastMessageTime, unreadCount
  }));
};

/**
 * Save assigned doctors to Firestore
 */
const saveAssignedDoctors = async (studentId: string, doctors: AssignedDoctor[]): Promise<void> => {
  try {
    await setDoc(doc(db, 'doctorAssignments', studentId), {
      doctors: doctors.map(doctor => ({
        id: doctor.id,
        uid: doctor.uid,
        name: doctor.name,
        specialty: doctor.specialty,
        department: doctor.department,
        email: doctor.email,
        assignedDate: doctor.assignedDate,
        lastMessage: doctor.lastMessage,
        lastMessageTime: doctor.lastMessageTime,
        unreadCount: doctor.unreadCount
      })),
      updatedAt: new Date().toISOString()
    });
    console.log(`💾 Saved doctor assignments to Firestore for student: ${studentId}`);
  } catch (error) {
    console.error('Error saving doctor assignments:', error);
    // No fallback to localStorage
  }
};

/**
 * Get assigned doctors for a student
 */
export const getAssignedDoctors = async (studentId: string): Promise<AssignedDoctor[]> => {
  try {
    const docRef = doc(db, 'doctorAssignments', studentId);
    const docSnap = await getDoc(docRef);
    if (docSnap.exists()) {
      const data = docSnap.data();
      return data.doctors || [];
    }
    return [];
  } catch (error) {
    console.error('Error fetching assigned doctors:', error);
    return [];
  }
};

/**
 * Get doctor assignment counts for load balancing
 */
const getDoctorAssignmentCounts = async (doctors: Doctor[]): Promise<Record<string, number>> => {
  try {
    const assignmentCounts: Record<string, number> = {};
    doctors.forEach(doctor => {
      assignmentCounts[doctor.id] = 0;
    });
    const assignmentsSnapshot = await getDocs(collection(db, 'doctorAssignments'));
    assignmentsSnapshot.forEach(docSnap => {
      const data = docSnap.data();
      if (data.doctors && Array.isArray(data.doctors)) {
        (data.doctors as { id: string }[]).forEach((assignedDoctor) => {
          if (Object.prototype.hasOwnProperty.call(assignmentCounts, assignedDoctor.id)) {
            assignmentCounts[assignedDoctor.id]++;
          }
        });
      }
    });
    return assignmentCounts;
  } catch (error) {
    console.error('Error getting doctor assignment counts:', error);
    const emptyCounts: Record<string, number> = {};
    doctors.forEach(doctor => {
      emptyCounts[doctor.id] = 0;
    });
    return emptyCounts;
  }
};

/**
 * Get all available doctors (for browsing and chat)
 * This function returns ALL doctors in the database for "Show All Doctors" functionality
 */
export const getAllDoctors = async (): Promise<Doctor[]> => {
  try {
    console.log('🔍 Getting ALL doctors for chat/browsing...');
    const doctors = await getDoctorsFromFirebase();
    console.log(`📊 Found ${doctors.length} real doctors from Firebase database`);

    if (doctors.length === 0) {
      console.warn('⚠️ No doctors found in database! Students will not be able to chat with anyone.');
    }

    const result = doctors.map(doctor => ({
      ...doctor,
      assigned: false, // Will be updated based on student's assignments
      requested: false
    }));

    console.log(`✅ Returning ${result.length} doctors for "Show All Doctors" functionality`);

    // Debug: Log first few doctors to verify data
    if (result.length > 0) {
      console.log('Sample doctors:', result.slice(0, 3).map(d => ({
        id: d.id,
        name: d.name,
        specialty: d.specialty,
        email: d.email
      })));
    }

    return result;
  } catch (error) {
    console.error('❌ Error fetching all doctors:', error);
    return [];
  }
};

/**
 * Debug function to check doctor data
 */
export const debugDoctorData = async () => {
  console.log('🔍 Debugging doctor data...');
  try {
    // Check raw user data
    const allUsers = await getAllUsers();
    console.log(`📊 Total users in system: ${allUsers.length}`);

    const doctorUsers = allUsers.filter(user => user.role === 'doctor');
    console.log(`👨‍⚕️ Users with doctor role: ${doctorUsers.length}`);

    // Log sample doctor users
    doctorUsers.slice(0, 3).forEach((user, index) => {
      console.log(`Doctor ${index + 1}:`, {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        role: user.role,
        specialty: user.specialty,
        department: user.department
      });
    });

    // Check processed doctors
    const processedDoctors = await getDoctorsFromFirebase();
    console.log(`🏥 Processed doctors: ${processedDoctors.length}`);

    return {
      totalUsers: allUsers.length,
      doctorUsers: doctorUsers.length,
      processedDoctors: processedDoctors.length,
      sampleDoctors: doctorUsers.slice(0, 3)
    };
  } catch (error) {
    console.error('❌ Error debugging doctor data:', error);
    return { error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

/**
 * Request assignment of a new doctor
 */
export const requestDoctorAssignment = async (studentId: string, doctorId: string): Promise<boolean> => {
  try {
    // TODO: Use studentId to create a real assignment request in Firestore in the future
    const doctors = await getDoctorsFromFirebase();
    const doctor = doctors.find(d => d.id === doctorId);
    if (!doctor) {
      return false;
    }
    // Optionally, create a Firestore document for the request here
    // ...
    return true;
  } catch (error) {
    console.error('Error requesting doctor assignment:', error);
    return false;
  }
};

/**
 * Get assignment statistics for admin dashboard
 */
export const getAssignmentStats = () => {
  return {
    totalDoctors: 0,
    totalStudents: 0,
    avgAssignmentsPerStudent: 0,
    totalAssignments: 0,
    coreSpecialtiesCoverage: '0%',
    mostRequestedSpecialty: '',
    leastRequestedSpecialty: ''
  };
};

/**
 * Fix existing doctors with missing UIDs
 */
export const fixDoctorsWithMissingUIDs = async () => {
  console.log('🔧 Fixing doctors with missing UIDs...');
  try {
    const { createUserProfile, getAllUsers } = await import('./authService');
    const allUsers = await getAllUsers();

    const doctorsWithoutUIDs = allUsers.filter(user =>
      user.role === 'doctor' && !user.uid
    );

    console.log(`Found ${doctorsWithoutUIDs.length} doctors without UIDs`);

    const fixedDoctors = [];

    for (const doctor of doctorsWithoutUIDs) {
      try {
        // Generate a proper UID
        const uid = `doctor_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Create updated profile with UID
        const updatedProfile = {
          ...doctor,
          uid: uid
        };

        console.log(`Fixing doctor: ${doctor.displayName || doctor.email} with new UID: ${uid}`);

        // Save with proper UID
        await createUserProfile(uid, updatedProfile);
        fixedDoctors.push(updatedProfile);

        console.log(`✅ Fixed doctor: ${doctor.displayName || doctor.email}`);
      } catch (error) {
        console.error(`❌ Failed to fix doctor ${doctor.displayName || doctor.email}:`, error);
      }
    }

    console.log(`🎉 Fixed ${fixedDoctors.length} doctors`);
    return fixedDoctors;
  } catch (error) {
    console.error('❌ Error fixing doctors:', error);
    return [];
  }
};

/**
 * Create test doctors for development/testing
 */
export const createTestDoctors = async () => {
  console.log('🏥 Creating test doctors...');
  try {
    // Import setDoc and doc from firebase/firestore
    const { setDoc, doc } = await import('firebase/firestore');
    const { db } = await import('./firebase');

    const testDoctors = [
      {
        email: '<EMAIL>',
        displayName: 'Dr. Sarah Smith',
        specialty: 'General Practice',
        department: 'Primary Care'
      },
      {
        email: '<EMAIL>',
        displayName: 'Dr. Michael Johnson',
        specialty: 'Mental Health',
        department: 'Psychiatry'
      },
      {
        email: '<EMAIL>',
        displayName: 'Dr. Emily Williams',
        specialty: 'Emergency Medicine',
        department: 'Emergency'
      }
    ];

    const createdDoctors = [];

    for (const doctor of testDoctors) {
      try {
        // Generate a proper UID
        const uid = `doctor_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Create a proper user profile for the doctor
        const doctorProfile = {
          uid: uid,
          email: doctor.email,
          displayName: doctor.displayName,
          role: 'doctor' as const,
          specialty: doctor.specialty,
          department: doctor.department,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        console.log(`Creating doctor with UID: ${uid}`);

        // Save to Firebase using the UID as document ID
        await setDoc(doc(db, 'users', uid), doctorProfile);
        createdDoctors.push(doctorProfile);
        console.log(`✅ Created doctor: ${doctor.displayName} with UID: ${uid}`);
      } catch (error) {
        console.error(`❌ Failed to create doctor ${doctor.displayName}:`, error);
      }
    }

    console.log(`🎉 Created ${createdDoctors.length} test doctors`);
    return createdDoctors;
  } catch (error) {
    console.error('❌ Error creating test doctors:', error);
    return [];
  }
};

/**
 * Clean up all doctor assignments from database
 * Since all doctors are now available to all students, assignments are no longer needed
 */
export const cleanupDoctorAssignments = async (): Promise<void> => {
  try {
    console.log('🧹 Cleaning up doctor assignments from database...');

    // Get all assignment documents
    const assignmentsSnapshot = await getDocs(collection(db, 'doctorAssignments'));
    console.log(`📊 Found ${assignmentsSnapshot.docs.length} assignment documents to clean up`);

    // Delete all assignment documents
    let deletedCount = 0;
    for (const docSnap of assignmentsSnapshot.docs) {
      try {
        await deleteDoc(doc(db, 'doctorAssignments', docSnap.id));
        deletedCount++;
      } catch (error) {
        console.error(`❌ Failed to delete assignment for ${docSnap.id}:`, error);
      }
    }

    console.log(`🎉 Successfully cleaned up ${deletedCount}/${assignmentsSnapshot.docs.length} assignment documents`);
    console.log('✅ All students now have access to all doctors without assignments');
  } catch (error) {
    console.error('❌ Error cleaning up doctor assignments:', error);
  }
};

/**
 * Comprehensive test to diagnose doctor loading issues
 */
export const testDoctorFlow = async () => {
  console.log('🔬 COMPREHENSIVE DOCTOR FLOW TEST');
  console.log('=====================================');

  try {
    // Step 1: Test getAllUsers
    console.log('1️⃣ Testing getAllUsers...');
    const allUsers = await getAllUsers();
    console.log(`   Found ${allUsers.length} total users`);

    // Step 2: Test getDoctorsFromFirebase
    console.log('2️⃣ Testing getDoctorsFromFirebase...');
    const doctors = await getDoctorsFromFirebase();
    console.log(`   Found ${doctors.length} doctors`);

    // Step 3: Test getAllDoctors
    console.log('3️⃣ Testing getAllDoctors...');
    const allDoctors = await getAllDoctors();
    console.log(`   Found ${allDoctors.length} available doctors`);

    // Step 4: If no doctors, create test doctors
    if (allDoctors.length === 0) {
      console.log('4️⃣ No doctors found, creating test doctors...');
      const created = await createTestDoctors();
      console.log(`   Created ${created.length} test doctors`);

      // Re-test after creation
      const newDoctors = await getAllDoctors();
      console.log(`   After creation: ${newDoctors.length} doctors available`);
    }

    console.log('✅ Test completed successfully');
    return true;
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
};

// Make debug functions available globally
if (typeof window !== 'undefined') {
  (window as any).debugDoctorData = debugDoctorData;
  (window as any).createTestDoctors = createTestDoctors;
  (window as any).fixDoctorsWithMissingUIDs = fixDoctorsWithMissingUIDs;
  (window as any).cleanupDoctorAssignments = cleanupDoctorAssignments;
  (window as any).testDoctorFlow = testDoctorFlow;
}
