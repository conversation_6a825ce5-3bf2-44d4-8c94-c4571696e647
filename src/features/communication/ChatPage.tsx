import React, { useState, useEffect, useRef } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemText,
  TextField,
  Button,
  Box,
  Avatar,
  Chip,
  CircularProgress,
  IconButton,
  Divider
} from '@mui/material';
import {
  Send as SendIcon,
  Search as SearchIcon,
  Phone as PhoneIcon,
  Videocam as VideocamIcon,
  FiberManualRecord as FiberManualRecordIcon,
  Mic as MicIcon,
  MicOff as MicOffIcon,
  VideocamOff as VideoOffIcon,
  CallEnd as CallEndIcon
} from '@mui/icons-material';
import Layout from '../../components/layout/Layout';
import { useFirebase } from '../../contexts/FirebaseContext';
import { useChat } from '../../hooks/useChat';
import { useWebRTC } from '../../hooks/useWebRTC';
import { getAssignedDoctors, getAllDoctors, requestDoctorAssignment, assignDoctorsToStudent, debugDoctorData, createTestDoctors, fixDoctorsWithMissingUIDs, testDoctorFlow, type AssignedDoctor, type Doctor } from '../../services/doctorAssignmentService';

const ChatPage = () => {
  const { currentUser } = useFirebase();
  const {
    conversations,
    selectedConversation,
    messages,
    loading,
    sending,
    typing,
    createConversation,
    selectConversation,
    sendMessage,
    handleTyping,
    getOtherParticipant,
    getUnreadCount
  } = useChat();

  const {
    currentCall,
    incomingCalls,
    localStream,
    remoteStream,
    isCallActive,
    isMuted,
    isVideoEnabled,
    callStatus,
    initiateCall,
    acceptCall,
    declineCall,
    endCall,
    toggleMute,
    toggleVideo
  } = useWebRTC();

  // Local state
  const [newMessage, setNewMessage] = useState('');
  const [allDoctors, setAllDoctors] = useState<Doctor[]>([]);
  const [assignedDoctors, setAssignedDoctors] = useState<AssignedDoctor[]>([]);
  const [showAllProviders, setShowAllProviders] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);

  // Load doctors data - NO ASSIGNMENTS NEEDED
  useEffect(() => {
    const loadDoctors = async () => {
      if (currentUser) {
        try {
          console.log('🔄 Loading ALL doctors for user:', currentUser.uid);

          // Only get all doctors - no assignments needed
          const all = await getAllDoctors();

          console.log('📊 Doctor data:', {
            allCount: all.length
          });

          // All doctors are available to all students - no assignments needed
          const allWithStatus = all.map(doctor => {
            const doctorId = doctor.id || doctor.uid;
            return {
              ...doctor,
              id: doctorId, // Ensure consistent ID
              assigned: false, // No assignments - all doctors available to all students
              requested: false
            };
          });

          // Set both assigned and all doctors to the same list
          // This makes all doctors available to all students
          setAssignedDoctors(allWithStatus);
          setAllDoctors(allWithStatus);

          console.log('All doctors loaded and available:', {
            total: allWithStatus.length
          });

          // Debug: Check for doctors without IDs
          const doctorsWithoutIds = allWithStatus.filter(d => !d.id && !d.uid);
          if (doctorsWithoutIds.length > 0) {
            console.warn('Found doctors without IDs:', doctorsWithoutIds);
          }

          // Debug: Log first few doctors
          console.log('Sample doctors:', allWithStatus.slice(0, 3).map(d => ({
            id: d.id,
            uid: d.uid,
            name: d.name,
            hasValidId: !!(d.id || d.uid)
          })));

          // Debug: Log the exact data that will be used for rendering
          console.log('🎯 Data for UI rendering:');
          console.log('- All doctors available to student:', allWithStatus.length);
          console.log('- Sample doctors:', allWithStatus.slice(0, 3));
        } catch (error) {
          console.error('Error loading doctors:', error);
          setAssignedDoctors([]);
          setAllDoctors([]);
        }
      }
    };

    loadDoctors();
  }, [currentUser]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Setup video streams
  useEffect(() => {
    if (localStream && localVideoRef.current) {
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);

  useEffect(() => {
    if (remoteStream && remoteVideoRef.current) {
      remoteVideoRef.current.srcObject = remoteStream;
    }
  }, [remoteStream]);

  // Handle message sending
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    try {
      await sendMessage(newMessage);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Failed to send message. Please try again.');
    }
  };

  // Handle doctor selection
  const handleSelectDoctor = async (doctor: AssignedDoctor | Doctor) => {
    if (!currentUser) {
      console.error('No current user found');
      alert('Please log in to start a conversation.');
      return;
    }

    // Validate doctor ID
    const doctorId = doctor.id || doctor.uid;
    if (!doctorId) {
      console.error('Doctor ID is missing:', doctor);
      alert('Unable to start conversation: Doctor information is incomplete.');
      return;
    }

    console.log('Starting conversation with doctor:', {
      doctorId,
      doctorName: doctor.name,
      currentUserId: currentUser.uid
    });

    try {
      const conversationId = await createConversation(doctorId);
      console.log('Conversation created/found:', conversationId);

      const conversation = conversations.find(c => c.id === conversationId);
      if (conversation) {
        selectConversation(conversation);
        console.log('Conversation selected successfully');
      } else {
        console.warn('Conversation created but not found in conversations list');
        // Force refresh conversations
        window.location.reload();
      }
    } catch (error) {
      console.error('Error selecting doctor:', error);
      alert(`Failed to start conversation with ${doctor.name}. Please try again.`);
    }
  };

  // Handle call initiation
  const handleStartVideoCall = async () => {
    if (!selectedConversation || !currentUser) return;

    const otherParticipant = getOtherParticipant(selectedConversation);
    if (!otherParticipant) return;

    try {
      const otherParticipantId = selectedConversation.participants.find(id => id !== currentUser.uid);
      if (otherParticipantId) {
        await initiateCall(selectedConversation.id, otherParticipantId, 'video');
      }
    } catch (error) {
      console.error('Error starting video call:', error);
      alert('Failed to start video call. Please try again.');
    }
  };

  const handleStartVoiceCall = async () => {
    if (!selectedConversation || !currentUser) return;

    const otherParticipant = getOtherParticipant(selectedConversation);
    if (!otherParticipant) return;

    try {
      const otherParticipantId = selectedConversation.participants.find(id => id !== currentUser.uid);
      if (otherParticipantId) {
        await initiateCall(selectedConversation.id, otherParticipantId, 'voice');
      }
    } catch (error) {
      console.error('Error starting voice call:', error);
      alert('Failed to start voice call. Please try again.');
    }
  };

  // Handle doctor request
  const handleRequestProvider = async (doctor: Doctor) => {
    if (!currentUser) return;

    const doctorId = doctor.id || doctor.uid;
    if (!doctorId) {
      alert('Unable to request doctor: Doctor information is incomplete.');
      return;
    }

    const updatedAllDoctors = allDoctors.map(d =>
      (d.id || d.uid) === doctorId ? { ...d, requested: true } : d
    );
    setAllDoctors(updatedAllDoctors);

    try {
      const success = await requestDoctorAssignment(currentUser.uid, doctorId);
      if (success) {
        alert(`✅ Request sent to connect with ${doctor.name}! You'll be notified when approved.`);
      } else {
        alert(`❌ Failed to send request. Please try again.`);
        const revertedDoctors = allDoctors.map(d =>
          (d.id || d.uid) === doctorId ? { ...d, requested: false } : d
        );
        setAllDoctors(revertedDoctors);
      }
    } catch (error) {
      console.error('Error requesting doctor assignment:', error);
      alert(`❌ Failed to send request. Please try again.`);
    }
  };

  // Debug function to test communication flow
  const debugCommunicationFlow = () => {
    console.log('=== COMMUNICATION DEBUG ===');
    console.log('Current User:', currentUser?.uid);
    console.log('Available Doctors:', allDoctors.length);
    console.log('Conversations:', conversations.length);
    console.log('NOTE: All doctors are available to all students - no assignments needed');

    console.log('Sample doctors:', allDoctors.slice(0, 3).map(d => ({
      id: d.id,
      uid: d.uid,
      name: d.name,
      specialty: d.specialty,
      hasValidId: !!(d.id || d.uid)
    })));

    return {
      currentUser: currentUser?.uid,
      availableDoctors: allDoctors.length,
      conversations: conversations.length,
      note: 'All doctors available to all students'
    };
  };

  // Make debug function available globally
  if (typeof window !== 'undefined') {
    (window as any).debugCommunicationFlow = debugCommunicationFlow;
  }

  // Filter doctors - only use allDoctors since all doctors are available to all students
  const filteredAllDoctors = allDoctors.filter(doctor =>
    doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doctor.specialty.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get current conversation participant
  const currentParticipant = selectedConversation ? getOtherParticipant(selectedConversation) : null;

  return (
    <Layout>
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Communication Portal
        </Typography>

        <Grid container spacing={3}>
          {/* Doctors List */}
          <Grid item xs={12} md={4}>
            <Paper sx={{
              p: 2,
              height: '70vh',
              borderRadius: 3,
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden'
            }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  All Healthcare Providers
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {allDoctors.length} available
                </Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />

              {/* Search Bar */}
              <TextField
                fullWidth
                size="small"
                placeholder="Search doctors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />
                }}
                sx={{ mb: 2, '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
              />

              <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
                {/* Debug: Log what's being rendered */}
                {console.log('🔍 UI Render Debug:', {
                  allDoctors: allDoctors.length,
                  filteredAllDoctors: filteredAllDoctors.length,
                  searchTerm,
                  renderingList: 'allDoctors',
                  actualData: filteredAllDoctors
                })}

                {allDoctors.length === 0 ? (
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '200px',
                    p: 3,
                    textAlign: 'center'
                  }}>
                    <Typography variant="h6" gutterBottom color="text.secondary">
                      Loading Doctors...
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Please wait while we load available healthcare providers.
                    </Typography>

                    {/* Debug Panel - Only show if there's actually an issue */}
                    <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 2, width: '100%' }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Debug Tools (If doctors don't load)
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', justifyContent: 'center' }}>
                        <Button
                          size="small"
                          variant="contained"
                          color="primary"
                          onClick={async () => {
                            console.log('🚀 Running comprehensive doctor test...');
                            const success = await testDoctorFlow();
                            if (success) {
                              alert('Test completed! Check console for details. Refreshing page...');
                              window.location.reload();
                            } else {
                              alert('Test failed. Check console for error details.');
                            }
                          }}
                        >
                          Fix & Load Doctors
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={async () => {
                            const result = await debugDoctorData();
                            console.log('Debug result:', result);
                            alert(`Found ${result.doctorUsers || 0} doctors in system. Check console for details.`);
                          }}
                        >
                          Check Doctors
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={async () => {
                            const result = await fixDoctorsWithMissingUIDs();
                            if (result.length > 0) {
                              alert(`Fixed ${result.length} doctors with missing UIDs. Refreshing page...`);
                              window.location.reload();
                            } else {
                              alert('No doctors found that need fixing. Creating new test doctors...');
                              const newDoctors = await createTestDoctors();
                              alert(`Created ${newDoctors.length} test doctors. Refreshing page...`);
                              window.location.reload();
                            }
                          }}
                        >
                          Fix/Create Doctors
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => {
                            debugCommunicationFlow();
                            alert('Debug info logged to console');
                          }}
                        >
                          Debug Flow
                        </Button>
                        <Button
                          size="small"
                          variant="contained"
                          onClick={async () => {
                            console.log('🔄 Manual refresh triggered');
                            try {
                              const all = await getAllDoctors();

                              console.log('📊 Manual refresh data:', { all: all.length });

                              // All doctors available to all students
                              const allWithStatus = all.map(doctor => ({
                                ...doctor,
                                assigned: false, // No assignments needed
                                requested: false
                              }));

                              setAssignedDoctors(allWithStatus);
                              setAllDoctors(allWithStatus);

                              alert(`Refreshed! Found ${all.length} doctors available to chat with.`);
                            } catch (error) {
                              console.error('Manual refresh error:', error);
                              alert('Refresh failed. Check console for details.');
                            }
                          }}
                        >
                          Refresh
                        </Button>
                      </Box>

                      {/* Temporary Debug Display */}
                      <Box sx={{ mt: 2, p: 2, bgcolor: 'yellow.100', borderRadius: 2, fontSize: '0.8rem' }}>
                        <Typography variant="caption" display="block">
                          Debug Info:
                        </Typography>
                        <Typography variant="caption" display="block">
                          mode: All doctors available to all students
                        </Typography>
                        <Typography variant="caption" display="block">
                          availableDoctors: {allDoctors.length}
                        </Typography>
                        <Typography variant="caption" display="block">
                          note: All doctors available to all students
                        </Typography>
                        <Typography variant="caption" display="block">
                          filteredDoctors: {filteredAllDoctors.length}
                        </Typography>
                        <Typography variant="caption" display="block">
                          searchTerm: "{searchTerm}"
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                ) : (
                  <List>
                    {filteredAllDoctors.map((doctor) => {
                      const doctorId = doctor.id || doctor.uid;
                      const isDisabled = !doctorId;

                      return (
                        <ListItem
                          key={doctor.id || doctor.uid || doctor.name}
                          sx={{
                            borderRadius: 2,
                            mb: 1,
                            cursor: isDisabled ? 'not-allowed' : 'pointer',
                            opacity: isDisabled ? 0.5 : 1,
                            '&:hover': {
                              bgcolor: isDisabled ? 'transparent' : 'rgba(0,114,255,0.08)'
                            }
                          }}
                          onClick={() => !isDisabled && handleSelectDoctor(doctor)}
                        >
                        <Avatar sx={{ mr: 2 }}>
                          {doctor.name.charAt(0)}
                        </Avatar>
                        <ListItemText
                          primary={doctor.name}
                          secondary={
                            <Box>
                              <Typography variant="body2">{doctor.specialty}</Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                                <FiberManualRecordIcon
                                  sx={{
                                    color: doctor.online ? 'success.main' : 'text.disabled',
                                    fontSize: 8,
                                    mr: 0.5
                                  }}
                                />
                                <Typography variant="caption">
                                  {doctor.online ? 'Online' : 'Offline'}
                                </Typography>
                              </Box>
                            </Box>
                          }
                        />
                        {!doctor.assigned && (
                          <Button
                            size="small"
                            variant="outlined"
                            disabled={doctor.requested || isDisabled}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRequestProvider(doctor);
                            }}
                          >
                            {doctor.requested ? 'Requested' : 'Request'}
                          </Button>
                        )}
                        {isDisabled && (
                          <Typography variant="caption" color="error" sx={{ ml: 1 }}>
                            Unavailable
                          </Typography>
                        )}
                        </ListItem>
                      );
                    })}
                  </List>
                )}
              </Box>
            </Paper>
          </Grid>

          {/* Chat Area */}
          <Grid item xs={12} md={8}>
            <Paper sx={{
              p: 0,
              height: '70vh',
              display: 'flex',
              flexDirection: 'column',
              borderRadius: 3,
              overflow: 'hidden',
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              position: 'relative'
            }}>
              {selectedConversation && currentParticipant ? (
                <>
                  {/* Chat Header */}
                  <Box sx={{
                    p: 2,
                    backgroundColor: 'primary.main',
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar sx={{ mr: 2 }}>
                        {currentParticipant.name.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle1" fontWeight="bold">
                          {currentParticipant.name}
                        </Typography>
                        <Typography variant="caption">
                          {currentParticipant.specialty}
                        </Typography>
                      </Box>
                    </Box>
                    <Box>
                      <IconButton
                        color="inherit"
                        size="small"
                        sx={{ mr: 1 }}
                        onClick={handleStartVoiceCall}
                        disabled={isCallActive}
                      >
                        <PhoneIcon />
                      </IconButton>
                      <IconButton
                        color="inherit"
                        size="small"
                        onClick={handleStartVideoCall}
                        disabled={isCallActive}
                      >
                        <VideocamIcon />
                      </IconButton>
                    </Box>
                  </Box>

                  {/* Messages Area */}
                  {loading ? (
                    <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                      <CircularProgress />
                    </Box>
                  ) : (
                    <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2, backgroundColor: '#f5f5f5' }}>
                      <List>
                        {messages.map((message) => (
                          <ListItem
                            key={message.id}
                            sx={{
                              textAlign: message.senderRole === 'user' ? 'right' : 'left',
                              mb: 2,
                              px: 2
                            }}
                          >
                            <Grid container spacing={2} direction={message.senderRole === 'user' ? 'row-reverse' : 'row'}>
                              <Grid item xs={1}>
                                <Avatar
                                  sx={{
                                    bgcolor: message.senderRole === 'user' ? 'primary.main' : 'secondary.main',
                                  }}
                                >
                                  {message.senderName.charAt(0)}
                                </Avatar>
                              </Grid>
                              <Grid item xs={11} sm={8} md={7}>
                                <Paper
                                  elevation={1}
                                  sx={{
                                    p: 2,
                                    bgcolor: message.senderRole === 'user' ? 'primary.light' : 'white',
                                    color: message.senderRole === 'user' ? 'white' : 'text.primary',
                                    borderRadius: message.senderRole === 'user'
                                      ? '20px 20px 5px 20px'
                                      : '20px 20px 20px 5px',
                                    boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
                                  }}
                                >
                                  <Typography variant="body1">{message.content}</Typography>
                                  <Typography variant="caption" sx={{ display: 'block', mt: 1, textAlign: 'right', opacity: 0.8 }}>
                                    {message.timestamp?.toDate?.()?.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) || 'Now'}
                                  </Typography>
                                </Paper>
                              </Grid>
                            </Grid>
                          </ListItem>
                        ))}
                        {typing.length > 0 && (
                          <ListItem sx={{ px: 2 }}>
                            <Typography variant="caption" color="text.secondary">
                              {typing.map(t => t.userName).join(', ')} {typing.length === 1 ? 'is' : 'are'} typing...
                            </Typography>
                          </ListItem>
                        )}
                        <div ref={messagesEndRef} />
                      </List>
                    </Box>
                  )}

                  {/* Message Input */}
                  <Box sx={{ p: 2, backgroundColor: 'background.paper', borderTop: '1px solid', borderColor: 'divider' }}>
                    <form onSubmit={handleSendMessage}>
                      <Grid container spacing={1}>
                        <Grid item xs={11}>
                          <TextField
                            fullWidth
                            placeholder="Type a message"
                            variant="outlined"
                            value={newMessage}
                            onChange={(e) => {
                              setNewMessage(e.target.value);
                              handleTyping();
                            }}
                            disabled={sending}
                            size="small"
                            sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                          />
                        </Grid>
                        <Grid item xs={1}>
                          <Button
                            variant="contained"
                            color="primary"
                            type="submit"
                            disabled={!newMessage.trim() || sending}
                            sx={{ minWidth: 0, p: 1, borderRadius: 2, height: '100%' }}
                          >
                            <SendIcon />
                          </Button>
                        </Grid>
                      </Grid>
                    </form>
                  </Box>
                </>
              ) : (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%',
                  p: 3,
                  textAlign: 'center'
                }}>
                  <Typography variant="h6" gutterBottom>
                    Welcome to the Chat Portal
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Select a healthcare provider to start a conversation
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default ChatPage;
